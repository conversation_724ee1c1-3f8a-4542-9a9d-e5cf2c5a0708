name: Publish JobSpy to PyPi
on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  build-n-publish:
    name: Build and publish JobSpy to PyPi
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: "3.10"

      - name: Install poetry
        run: >-
          python3 -m
          pip install
          poetry
          --user

      - name: Build distribution 📦
        run: >-
          python3 -m
          poetry
          build

      - name: Publish distribution 📦 to PyPI
        if: startsWith(github.ref, 'refs/tags') || github.event_name == 'workflow_dispatch'
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          password: ${{ secrets.PYPI_API_TOKEN }}