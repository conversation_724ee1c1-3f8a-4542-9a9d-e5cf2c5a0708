[build-system]
requires = [ "poetry-core",]
build-backend = "poetry.core.masonry.api"

[tool.poetry]
name = "python-jobspy"
version = "1.1.80"
description = "Job scraper for LinkedIn, Indeed, Glassdoor, ZipRecruiter & Bayt"
authors = ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>"]
homepage = "https://github.com/cullenwatson/JobSpy"
readme = "README.md"
keywords = [ "jobs-scraper", "linkedin", "indeed", "glassdoor", "ziprecruiter", "bayt", "naukri"]
[[tool.poetry.packages]]
include = "jobspy"

[tool.black]
line-length = 88

[tool.poetry.dependencies]
python = "^3.10"
requests = "^2.31.0"
beautifulsoup4 = "^4.12.2"
pandas = "^2.1.0"
NUMPY = "1.26.3"
pydantic = "^2.3.0"
tls-client = "^1.0.1"
markdownify = "^0.13.1"
regex = "^2024.4.28"

[tool.poetry.group.dev.dependencies]
jupyter = "^1.0.0"
black = "*"
pre-commit = "*"
